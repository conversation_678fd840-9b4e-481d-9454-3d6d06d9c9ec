
import { useState, useEffect } from 'react';
import LandingPage from '@/components/LandingPage';
import GeneratePlan from '@/components/GeneratePlan';
import PlanApproval from '@/components/PlanApproval';
import Dashboard from '@/components/Dashboard';
import { Toaster } from "@/components/ui/toaster";
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarInset
} from "@/components/ui/sidebar";

const Index = () => {
  const [currentPage, setCurrentPage] = useState('landing');

  useEffect(() => {
    // Check localStorage for any saved navigation state
    const savedPage = localStorage.getItem('vitalEdgePage');
    if (savedPage) {
      setCurrentPage(savedPage);
    }
  }, []);

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    // Save navigation state to localStorage
    localStorage.setItem('vitalEdgePage', page);
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'landing':
        return <LandingPage onNavigate={handleNavigate} />;
      case 'generate':
        return <GeneratePlan onNavigate={handleNavigate} />;
      case 'approval':
        return <PlanApproval onNavigate={handleNavigate} />;
      case 'dashboard':
        return (
          <SidebarProvider>
            <div className="flex w-full">
              <Dashboard onNavigate={handleNavigate} />
            </div>
          </SidebarProvider>
        );
      default:
        return <LandingPage onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="overflow-x-hidden">
      {renderPage()}
      <Toaster />
    </div>
  );
};

export default Index;
