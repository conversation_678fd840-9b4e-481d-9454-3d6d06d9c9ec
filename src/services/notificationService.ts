import { userStore, type ReminderItem } from '@/store/userStore';

class NotificationService {
  private checkInterval: NodeJS.Timeout | null = null;
  private notifiedReminders = new Set<string>(); // Track which reminders we've already notified for today

  constructor() {
    this.requestPermission();
    this.startChecking();
  }

  // Request notification permission
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      console.warn('Notifications are blocked by user');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  // Check if it's time for any reminders
  private checkReminders(): void {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
    const today = now.toDateString();

    const reminders = userStore.getReminders();

    reminders.forEach(reminder => {
      // Skip if reminder is disabled or already completed
      if (!reminder.enabled || reminder.completed) return;

      // Check if reminder should fire today
      const shouldFireToday = reminder.days.includes('daily') || reminder.days.includes(currentDay);
      if (!shouldFireToday) return;

      // Check if it's the right time
      if (reminder.time === currentTime) {
        const notificationKey = `${reminder.id}-${today}`;
        
        // Don't notify if we already notified for this reminder today
        if (this.notifiedReminders.has(notificationKey)) return;

        this.showNotification(reminder);
        this.notifiedReminders.add(notificationKey);
      }
    });

    // Clean up old notification keys (older than today)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toDateString();
    
    this.notifiedReminders.forEach(key => {
      if (key.includes(yesterdayStr)) {
        this.notifiedReminders.delete(key);
      }
    });
  }

  // Show notification for a reminder
  private async showNotification(reminder: ReminderItem): Promise<void> {
    const hasPermission = await this.requestPermission();
    if (!hasPermission) return;

    const iconMap = {
      workout: '💪',
      nutrition: '🥗',
      hydration: '💧',
      general: '⏰'
    };

    const icon = iconMap[reminder.type] || '⏰';
    const title = `VitalEdge Reminder ${icon}`;
    const body = reminder.text;

    try {
      const notification = new Notification(title, {
        body,
        icon: '/favicon.ico', // You can add a custom icon here
        badge: '/favicon.ico',
        tag: `reminder-${reminder.id}`, // Prevents duplicate notifications
        requireInteraction: true, // Keeps notification visible until user interacts
        actions: [
          {
            action: 'complete',
            title: 'Mark Complete'
          },
          {
            action: 'dismiss',
            title: 'Dismiss'
          }
        ]
      });

      // Handle notification click
      notification.onclick = () => {
        window.focus(); // Bring the app to focus
        notification.close();
        
        // Optionally navigate to reminders tab
        // You can emit an event here to navigate to reminders
        console.log('Notification clicked for reminder:', reminder.text);
      };

      // Auto-close after 10 seconds if not interacted with
      setTimeout(() => {
        notification.close();
      }, 10000);

      console.log(`📢 Notification shown for reminder: ${reminder.text}`);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  // Start checking for reminders every minute
  startChecking(): void {
    if (this.checkInterval) return; // Already checking

    console.log('🔔 Notification service started');
    
    // Check immediately
    this.checkReminders();
    
    // Then check every minute
    this.checkInterval = setInterval(() => {
      this.checkReminders();
    }, 60000); // Check every minute
  }

  // Stop checking for reminders
  stopChecking(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🔕 Notification service stopped');
    }
  }

  // Test notification (for debugging)
  async testNotification(): Promise<void> {
    const hasPermission = await this.requestPermission();
    if (!hasPermission) {
      console.warn('Cannot show test notification - permission denied');
      return;
    }

    const notification = new Notification('VitalEdge Test 🧪', {
      body: 'This is a test notification to verify everything is working!',
      icon: '/favicon.ico',
      tag: 'test-notification'
    });

    setTimeout(() => {
      notification.close();
    }, 5000);

    console.log('🧪 Test notification shown');
  }

  // Get notification permission status
  getPermissionStatus(): NotificationPermission {
    if (!('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  }

  // Check if notifications are supported
  isSupported(): boolean {
    return 'Notification' in window;
  }

  // Manually trigger notification for a specific reminder (for testing)
  triggerReminderNotification(reminderId: number): void {
    const reminders = userStore.getReminders();
    const reminder = reminders.find(r => r.id === reminderId);
    
    if (reminder && reminder.enabled) {
      this.showNotification(reminder);
    }
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

// Export for manual control if needed
export default NotificationService;
