interface UserData {
  calories: number;
  waterIntake: number;
  streak: number;
  xp: number;
  level: number;
  dailyGoal: number;
  waterGoal: number;
}

interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

class AIService {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1/chat/completions';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
    if (!this.apiKey) {
      console.warn('OpenAI API key not found. AI features will use fallback responses.');
    }
  }

  private getSystemPrompt(userData?: UserData): string {
    const userStats = userData ? `
Current User Stats:
- Calories consumed: ${userData.calories}/${userData.dailyGoal} (${Math.round((userData.calories / userData.dailyGoal) * 100)}%)
- Water intake: ${userData.waterIntake}L/${userData.waterGoal}L (${Math.round((userData.waterIntake / userData.waterGoal) * 100)}%)
- Current streak: ${userData.streak} days
- Experience points: ${userData.xp} XP
- Level: ${userData.level}
` : '';

    return `You are VitalEdge Assistant, an expert AI health and fitness coach integrated into the VitalEdge wellness platform. You provide personalized, actionable advice on fitness, nutrition, hydration, and overall wellness.

${userStats}

Your personality and approach:
- Encouraging and motivational, but realistic
- Evidence-based recommendations
- Personalized advice based on user's current progress
- Concise but comprehensive responses
- Use emojis sparingly but effectively
- Focus on sustainable habits over quick fixes

Key areas of expertise:
1. **Fitness & Exercise**: Workout planning, form correction, progression strategies
2. **Nutrition**: Meal planning, calorie management, macro/micronutrients
3. **Hydration**: Water intake optimization, timing recommendations
4. **Recovery**: Sleep optimization, rest day planning, stress management
5. **Goal Setting**: SMART goals, habit formation, progress tracking

Response guidelines:
- Keep responses under 200 words unless detailed explanation is needed
- Always consider the user's current stats when giving advice
- Provide actionable next steps
- Acknowledge their progress and streak when relevant
- If asked about medical conditions, recommend consulting healthcare professionals
- Stay within your expertise - fitness, nutrition, and wellness

Remember: You're not just answering questions, you're helping users build sustainable healthy habits and achieve their wellness goals.`;
  }

  private getFallbackResponse(userMessage: string, userData?: UserData): string {
    const lowerCaseMessage = userMessage.toLowerCase();
    
    // Workout related questions
    if (lowerCaseMessage.includes("workout") || lowerCaseMessage.includes("exercise") || lowerCaseMessage.includes("training")) {
      if (lowerCaseMessage.includes("best") || lowerCaseMessage.includes("recommend")) {
        return `Based on your current progress, I recommend focusing on compound movements like squats, deadlifts, and push-ups. Your ${userData?.streak || 0}-day streak shows great consistency! 💪\n\nFor optimal results:\n• 3-4 strength sessions per week\n• 2-3 cardio sessions\n• Always include rest days\n\nWhat specific area would you like to focus on?`;
      } else if (lowerCaseMessage.includes("often") || lowerCaseMessage.includes("frequency")) {
        return `Great question! For sustainable progress, aim for 4-5 workout days per week. Your current ${userData?.streak || 0}-day streak is impressive! 🔥\n\nIdeal weekly split:\n• 3 strength training days\n• 2 cardio/conditioning days\n• 2 active recovery days\n\nQuality over quantity - consistency beats intensity every time!`;
      }
      return `Exercise is crucial for both physical and mental health. Your dedication shows with your current streak! Focus on progressive overload and proper form. What specific workout questions do you have?`;
    }

    // Nutrition related questions
    if (lowerCaseMessage.includes("food") || lowerCaseMessage.includes("diet") || lowerCaseMessage.includes("nutrition") || lowerCaseMessage.includes("eat")) {
      if (userData) {
        const calorieProgress = Math.round((userData.calories / userData.dailyGoal) * 100);
        return `Looking at your nutrition today - you're at ${userData.calories}/${userData.dailyGoal} calories (${calorieProgress}%). ${calorieProgress < 80 ? "You might need more fuel for your workouts!" : calorieProgress > 120 ? "You're a bit over your goal, but that's okay occasionally!" : "Great job staying on track!"} 🍎\n\nKey nutrition tips:\n• Prioritize protein (0.8-1g per lb bodyweight)\n• Include vegetables with every meal\n• Stay hydrated\n• Time carbs around workouts\n\nWhat specific nutrition goals are you working on?`;
      }
      return `Nutrition is 70% of your results! Focus on whole foods, adequate protein, and staying consistent. What specific nutrition questions do you have?`;
    }

    // Hydration related questions
    if (lowerCaseMessage.includes("water") || lowerCaseMessage.includes("hydration") || lowerCaseMessage.includes("drink")) {
      if (userData) {
        const hydrationProgress = Math.round((userData.waterIntake / userData.waterGoal) * 100);
        return `Your hydration today: ${userData.waterIntake}L/${userData.waterGoal}L (${hydrationProgress}%) 💧\n\n${hydrationProgress < 70 ? "Time to drink up! Try setting hourly reminders." : hydrationProgress > 100 ? "Excellent hydration! You're crushing your goals!" : "Good progress! Keep sipping throughout the day."}\n\nHydration tips:\n• Drink 16-20oz upon waking\n• Sip consistently vs. chugging\n• Add electrolytes for intense workouts\n• Monitor urine color as a guide`;
      }
      return `Proper hydration is essential for performance and recovery. Aim for at least 8 glasses daily, more if you're active. How's your water intake today?`;
    }

    // Progress and motivation
    if (lowerCaseMessage.includes("progress") || lowerCaseMessage.includes("motivation") || lowerCaseMessage.includes("streak")) {
      if (userData) {
        return `Your progress is fantastic! 🌟\n\n• ${userData.streak}-day streak - incredible consistency!\n• Level ${userData.level} with ${userData.xp} XP earned\n• Nutrition: ${Math.round((userData.calories / userData.dailyGoal) * 100)}% of daily goal\n• Hydration: ${Math.round((userData.waterIntake / userData.waterGoal) * 100)}% of target\n\nRemember: Progress isn't always linear. You're building lifelong habits, and that takes time. Every day you show up, you're winning! 🏆\n\nWhat's your biggest challenge right now?`;
      }
      return `Every day you choose health is a victory! Consistency beats perfection. What specific area would you like to improve?`;
    }

    // Default responses
    const defaultResponses = [
      `I'm here to help with your fitness and wellness journey! Your consistency is paying off. What specific area would you like to focus on today? 💪`,
      `Great to see you staying engaged with your health goals! Whether it's workouts, nutrition, or hydration - I'm here to help. What's on your mind? 🌟`,
      `Your dedication to wellness is inspiring! I can help with workout planning, nutrition advice, or any health-related questions. What would you like to explore? 🎯`,
      `Consistency is key, and you're demonstrating that beautifully! How can I support your wellness journey today? 🚀`
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }

  async generateResponse(userMessage: string, userData?: UserData): Promise<string> {
    // If no API key, use fallback
    if (!this.apiKey) {
      return this.getFallbackResponse(userMessage, userData);
    }

    try {
      const messages: AIMessage[] = [
        {
          role: 'system',
          content: this.getSystemPrompt(userData)
        },
        {
          role: 'user',
          content: userMessage
        }
      ];

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: messages,
          max_tokens: 300,
          temperature: 0.7,
          presence_penalty: 0.1,
          frequency_penalty: 0.1
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || this.getFallbackResponse(userMessage, userData);
    } catch (error) {
      console.error('AI Service Error:', error);
      return this.getFallbackResponse(userMessage, userData);
    }
  }
}

export const aiService = new AIService();
export type { UserData };
