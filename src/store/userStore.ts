interface UserData {
  calories: number;
  waterIntake: number;
  streak: number;
  xp: number;
  level: number;
  dailyGoal: number;
  waterGoal: number;
  lastActiveDate: string;
}

interface WorkoutData {
  name: string;
  duration: string;
  exercises: string[];
  completed: boolean;
  completedDate?: string;
}

interface FoodEntry {
  id: string;
  name: string;
  calories: number;
  timestamp: string;
}

interface ReminderItem {
  id: number;
  text: string;
  time: string;
  type: 'workout' | 'nutrition' | 'hydration' | 'general';
  completed: boolean;
  enabled: boolean;
  days: string[]; // ['monday', 'tuesday', etc.] or ['daily'] for every day
}

interface StatisticsData {
  weeklyWorkouts: number;
  weeklyNutritionGoals: number;
  weeklyHydrationGoals: number;
  monthlyWorkouts: number;
  monthlyXP: number;
  weeklyProgress: number[]; // 7 days of progress percentages
  achievements: {
    sevenDayStreak: boolean;
    twentyWorkouts: boolean;
    hydrationMaster: number; // days completed
    nutritionPro: number; // days completed
  };
}

class UserStore {
  private storageKey = 'vitaledge-user-data';
  private workoutKey = 'vitaledge-workout-data';
  private foodKey = 'vitaledge-food-entries';
  private remindersKey = 'vitaledge-reminders';
  private statisticsKey = 'vitaledge-statistics';

  // Get user data with defaults
  getUserData(): UserData {
    const saved = localStorage.getItem(this.storageKey);
    if (saved) {
      const data = JSON.parse(saved);
      // Check if it's a new day and update streak
      return this.updateStreakIfNeeded(data);
    }

    // Default data for new users
    const defaultData: UserData = {
      calories: 0,
      waterIntake: 0,
      streak: 0,
      xp: 0,
      level: 1,
      dailyGoal: 2000,
      waterGoal: 3.0,
      lastActiveDate: new Date().toDateString()
    };

    this.saveUserData(defaultData);
    return defaultData;
  }

  // Save user data
  saveUserData(data: UserData): void {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // Update streak if it's a new day
  private updateStreakIfNeeded(data: UserData): UserData {
    const today = new Date().toDateString();
    const lastActive = new Date(data.lastActiveDate).toDateString();

    if (today !== lastActive) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toDateString();

      // If last active was yesterday, continue streak
      // If it was longer ago, reset streak
      if (lastActive === yesterdayStr) {
        // Continue streak - no change needed
      } else {
        // Reset streak and daily progress
        data.streak = 0;
        data.calories = 0;
        data.waterIntake = 0;
      }

      data.lastActiveDate = today;
      this.saveUserData(data);
    }

    return data;
  }

  // Add calories
  addCalories(amount: number): UserData {
    const data = this.getUserData();
    data.calories += amount;
    data.xp += Math.floor(amount / 10); // 1 XP per 10 calories
    data.level = Math.floor(data.xp / 1000) + 1;
    this.saveUserData(data);
    return data;
  }

  // Add water
  addWater(amount: number): UserData {
    const data = this.getUserData();
    data.waterIntake += amount;
    data.xp += Math.floor(amount * 10); // 10 XP per liter
    data.level = Math.floor(data.xp / 1000) + 1;
    this.saveUserData(data);
    return data;
  }

  // Complete workout
  completeWorkout(): UserData {
    const data = this.getUserData();
    const workout = this.getWorkout();

    if (!workout.completed) {
      workout.completed = true;
      workout.completedDate = new Date().toISOString();
      this.saveWorkout(workout);

      // Award XP and update streak
      data.xp += 100; // 100 XP for completing workout
      data.streak += 1;
      data.level = Math.floor(data.xp / 1000) + 1;
      this.saveUserData(data);
    }

    return data;
  }

  // Get workout data
  getWorkout(): WorkoutData {
    const saved = localStorage.getItem(this.workoutKey);
    if (saved) {
      const workout = JSON.parse(saved);
      // Check if it's a new day and reset completion
      const today = new Date().toDateString();
      const completedDate = workout.completedDate ? new Date(workout.completedDate).toDateString() : null;

      if (completedDate !== today) {
        workout.completed = false;
        workout.completedDate = undefined;
        this.saveWorkout(workout);
      }

      return workout;
    }

    // Default workout
    const defaultWorkout: WorkoutData = {
      name: "Upper Body Strength",
      duration: "45 minutes",
      exercises: [
        "Push-ups (3 sets of 12)",
        "Pull-ups (3 sets of 8)",
        "Dumbbell rows (3 sets of 10)",
        "Shoulder press (3 sets of 10)"
      ],
      completed: false
    };

    this.saveWorkout(defaultWorkout);
    return defaultWorkout;
  }

  // Save workout data
  saveWorkout(workout: WorkoutData): void {
    localStorage.setItem(this.workoutKey, JSON.stringify(workout));
  }

  // Get food entries for today
  getTodaysFoodEntries(): FoodEntry[] {
    const saved = localStorage.getItem(this.foodKey);
    if (!saved) return [];

    const allEntries: FoodEntry[] = JSON.parse(saved);
    const today = new Date().toDateString();

    return allEntries.filter(entry =>
      new Date(entry.timestamp).toDateString() === today
    );
  }

  // Add food entry
  addFoodEntry(name: string, calories: number): FoodEntry {
    const entry: FoodEntry = {
      id: Date.now().toString(),
      name,
      calories,
      timestamp: new Date().toISOString()
    };

    const saved = localStorage.getItem(this.foodKey);
    const allEntries: FoodEntry[] = saved ? JSON.parse(saved) : [];
    allEntries.push(entry);

    localStorage.setItem(this.foodKey, JSON.stringify(allEntries));

    // Update user calories
    this.addCalories(calories);

    return entry;
  }

  // Get reminders
  getReminders(): ReminderItem[] {
    const saved = localStorage.getItem(this.remindersKey);
    if (saved) {
      return JSON.parse(saved);
    }

    // Default reminders
    const defaultReminders: ReminderItem[] = [
      { id: 1, text: "Morning workout", time: "07:00", type: "workout", completed: false, enabled: true, days: ['daily'] },
      { id: 2, text: "Drink water", time: "10:00", type: "hydration", completed: false, enabled: true, days: ['daily'] },
      { id: 3, text: "Healthy lunch", time: "12:30", type: "nutrition", completed: false, enabled: true, days: ['daily'] },
      { id: 4, text: "Post-workout protein", time: "17:30", type: "nutrition", completed: false, enabled: true, days: ['daily'] }
    ];

    this.saveReminders(defaultReminders);
    return defaultReminders;
  }

  // Save reminders
  saveReminders(reminders: ReminderItem[]): void {
    localStorage.setItem(this.remindersKey, JSON.stringify(reminders));
  }

  // Toggle reminder completion
  toggleReminder(id: number): ReminderItem[] {
    const reminders = this.getReminders();
    const updated = reminders.map(reminder =>
      reminder.id === id
        ? { ...reminder, completed: !reminder.completed }
        : reminder
    );

    this.saveReminders(updated);

    // Award XP for completing reminders
    const completedReminder = updated.find(r => r.id === id);
    if (completedReminder?.completed) {
      const userData = this.getUserData();
      userData.xp += 25; // 25 XP for completing a reminder
      userData.level = Math.floor(userData.xp / 1000) + 1;
      this.saveUserData(userData);
    }

    return updated;
  }

  // Add new reminder
  addReminder(text: string, time: string, type: ReminderItem['type'], days: string[] = ['daily']): ReminderItem {
    const reminders = this.getReminders();
    const newReminder: ReminderItem = {
      id: Math.max(...reminders.map(r => r.id), 0) + 1,
      text,
      time,
      type,
      completed: false,
      enabled: true,
      days
    };

    reminders.push(newReminder);
    this.saveReminders(reminders);
    return newReminder;
  }

  // Delete reminder
  deleteReminder(id: number): ReminderItem[] {
    const reminders = this.getReminders();
    const updated = reminders.filter(r => r.id !== id);
    this.saveReminders(updated);
    return updated;
  }

  // Toggle reminder enabled/disabled
  toggleReminderEnabled(id: number): ReminderItem[] {
    const reminders = this.getReminders();
    const updated = reminders.map(reminder =>
      reminder.id === id
        ? { ...reminder, enabled: !reminder.enabled }
        : reminder
    );
    this.saveReminders(updated);
    return updated;
  }

  // Get statistics
  getStatistics(): StatisticsData {
    const saved = localStorage.getItem(this.statisticsKey);
    if (saved) {
      return JSON.parse(saved);
    }

    // Calculate real statistics from user data
    const userData = this.getUserData();
    const workoutData = this.getWorkout();

    const defaultStats: StatisticsData = {
      weeklyWorkouts: workoutData.completed ? 1 : 0,
      weeklyNutritionGoals: userData.calories >= userData.dailyGoal ? 1 : 0,
      weeklyHydrationGoals: userData.waterIntake >= userData.waterGoal ? 1 : 0,
      monthlyWorkouts: workoutData.completed ? 1 : 0,
      monthlyXP: userData.xp,
      weeklyProgress: [40, 65, 45, 80, 70, 90, userData.calories / userData.dailyGoal * 100],
      achievements: {
        sevenDayStreak: userData.streak >= 7,
        twentyWorkouts: false, // Will be calculated based on workout history
        hydrationMaster: userData.waterIntake >= userData.waterGoal ? 1 : 0,
        nutritionPro: userData.calories >= userData.dailyGoal ? 1 : 0
      }
    };

    this.saveStatistics(defaultStats);
    return defaultStats;
  }

  // Save statistics
  saveStatistics(stats: StatisticsData): void {
    localStorage.setItem(this.statisticsKey, JSON.stringify(stats));
  }

  // Update statistics when user completes actions
  updateStatistics(): void {
    const userData = this.getUserData();
    const workoutData = this.getWorkout();
    const stats = this.getStatistics();

    // Update weekly goals
    if (workoutData.completed) stats.weeklyWorkouts = Math.min(stats.weeklyWorkouts + 1, 7);
    if (userData.calories >= userData.dailyGoal) stats.weeklyNutritionGoals = Math.min(stats.weeklyNutritionGoals + 1, 7);
    if (userData.waterIntake >= userData.waterGoal) stats.weeklyHydrationGoals = Math.min(stats.weeklyHydrationGoals + 1, 7);

    // Update achievements
    stats.achievements.sevenDayStreak = userData.streak >= 7;
    if (userData.waterIntake >= userData.waterGoal) stats.achievements.hydrationMaster++;
    if (userData.calories >= userData.dailyGoal) stats.achievements.nutritionPro++;

    // Update weekly progress (last 7 days)
    const todayProgress = Math.min((userData.calories / userData.dailyGoal) * 100, 100);
    stats.weeklyProgress = [...stats.weeklyProgress.slice(1), todayProgress];

    this.saveStatistics(stats);
  }

  // Reset daily data (for testing)
  resetDailyData(): void {
    const data = this.getUserData();
    data.calories = 0;
    data.waterIntake = 0;
    data.lastActiveDate = new Date().toDateString();
    this.saveUserData(data);

    const workout = this.getWorkout();
    workout.completed = false;
    workout.completedDate = undefined;
    this.saveWorkout(workout);

    const reminders = this.getReminders();
    const resetReminders = reminders.map(r => ({ ...r, completed: false }));
    this.saveReminders(resetReminders);
  }
}

export const userStore = new UserStore();
export type { UserData, WorkoutData, FoodEntry, ReminderItem, StatisticsData };
