interface UserData {
  calories: number;
  waterIntake: number;
  streak: number;
  xp: number;
  level: number;
  dailyGoal: number;
  waterGoal: number;
  lastActiveDate: string;
}

interface WorkoutData {
  name: string;
  duration: string;
  exercises: string[];
  completed: boolean;
  completedDate?: string;
}

interface FoodEntry {
  id: string;
  name: string;
  calories: number;
  timestamp: string;
}

interface ReminderItem {
  id: number;
  text: string;
  time: string;
  type: 'workout' | 'nutrition' | 'hydration' | 'general';
  completed: boolean;
}

class UserStore {
  private storageKey = 'vitaledge-user-data';
  private workoutKey = 'vitaledge-workout-data';
  private foodKey = 'vitaledge-food-entries';
  private remindersKey = 'vitaledge-reminders';

  // Get user data with defaults
  getUserData(): UserData {
    const saved = localStorage.getItem(this.storageKey);
    if (saved) {
      const data = JSON.parse(saved);
      // Check if it's a new day and update streak
      return this.updateStreakIfNeeded(data);
    }
    
    // Default data for new users
    const defaultData: UserData = {
      calories: 0,
      waterIntake: 0,
      streak: 0,
      xp: 0,
      level: 1,
      dailyGoal: 2000,
      waterGoal: 3.0,
      lastActiveDate: new Date().toDateString()
    };
    
    this.saveUserData(defaultData);
    return defaultData;
  }

  // Save user data
  saveUserData(data: UserData): void {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  // Update streak if it's a new day
  private updateStreakIfNeeded(data: UserData): UserData {
    const today = new Date().toDateString();
    const lastActive = new Date(data.lastActiveDate).toDateString();
    
    if (today !== lastActive) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toDateString();
      
      // If last active was yesterday, continue streak
      // If it was longer ago, reset streak
      if (lastActive === yesterdayStr) {
        // Continue streak - no change needed
      } else {
        // Reset streak and daily progress
        data.streak = 0;
        data.calories = 0;
        data.waterIntake = 0;
      }
      
      data.lastActiveDate = today;
      this.saveUserData(data);
    }
    
    return data;
  }

  // Add calories
  addCalories(amount: number): UserData {
    const data = this.getUserData();
    data.calories += amount;
    data.xp += Math.floor(amount / 10); // 1 XP per 10 calories
    data.level = Math.floor(data.xp / 1000) + 1;
    this.saveUserData(data);
    return data;
  }

  // Add water
  addWater(amount: number): UserData {
    const data = this.getUserData();
    data.waterIntake += amount;
    data.xp += Math.floor(amount * 10); // 10 XP per liter
    data.level = Math.floor(data.xp / 1000) + 1;
    this.saveUserData(data);
    return data;
  }

  // Complete workout
  completeWorkout(): UserData {
    const data = this.getUserData();
    const workout = this.getWorkout();
    
    if (!workout.completed) {
      workout.completed = true;
      workout.completedDate = new Date().toISOString();
      this.saveWorkout(workout);
      
      // Award XP and update streak
      data.xp += 100; // 100 XP for completing workout
      data.streak += 1;
      data.level = Math.floor(data.xp / 1000) + 1;
      this.saveUserData(data);
    }
    
    return data;
  }

  // Get workout data
  getWorkout(): WorkoutData {
    const saved = localStorage.getItem(this.workoutKey);
    if (saved) {
      const workout = JSON.parse(saved);
      // Check if it's a new day and reset completion
      const today = new Date().toDateString();
      const completedDate = workout.completedDate ? new Date(workout.completedDate).toDateString() : null;
      
      if (completedDate !== today) {
        workout.completed = false;
        workout.completedDate = undefined;
        this.saveWorkout(workout);
      }
      
      return workout;
    }
    
    // Default workout
    const defaultWorkout: WorkoutData = {
      name: "Upper Body Strength",
      duration: "45 minutes",
      exercises: [
        "Push-ups (3 sets of 12)",
        "Pull-ups (3 sets of 8)",
        "Dumbbell rows (3 sets of 10)",
        "Shoulder press (3 sets of 10)"
      ],
      completed: false
    };
    
    this.saveWorkout(defaultWorkout);
    return defaultWorkout;
  }

  // Save workout data
  saveWorkout(workout: WorkoutData): void {
    localStorage.setItem(this.workoutKey, JSON.stringify(workout));
  }

  // Get food entries for today
  getTodaysFoodEntries(): FoodEntry[] {
    const saved = localStorage.getItem(this.foodKey);
    if (!saved) return [];
    
    const allEntries: FoodEntry[] = JSON.parse(saved);
    const today = new Date().toDateString();
    
    return allEntries.filter(entry => 
      new Date(entry.timestamp).toDateString() === today
    );
  }

  // Add food entry
  addFoodEntry(name: string, calories: number): FoodEntry {
    const entry: FoodEntry = {
      id: Date.now().toString(),
      name,
      calories,
      timestamp: new Date().toISOString()
    };
    
    const saved = localStorage.getItem(this.foodKey);
    const allEntries: FoodEntry[] = saved ? JSON.parse(saved) : [];
    allEntries.push(entry);
    
    localStorage.setItem(this.foodKey, JSON.stringify(allEntries));
    
    // Update user calories
    this.addCalories(calories);
    
    return entry;
  }

  // Get reminders
  getReminders(): ReminderItem[] {
    const saved = localStorage.getItem(this.remindersKey);
    if (saved) {
      return JSON.parse(saved);
    }
    
    // Default reminders
    const defaultReminders: ReminderItem[] = [
      { id: 1, text: "Morning workout", time: "7:00 AM", type: "workout", completed: false },
      { id: 2, text: "Drink water", time: "10:00 AM", type: "hydration", completed: false },
      { id: 3, text: "Healthy lunch", time: "12:30 PM", type: "nutrition", completed: false },
      { id: 4, text: "Post-workout protein", time: "5:30 PM", type: "nutrition", completed: false }
    ];
    
    this.saveReminders(defaultReminders);
    return defaultReminders;
  }

  // Save reminders
  saveReminders(reminders: ReminderItem[]): void {
    localStorage.setItem(this.remindersKey, JSON.stringify(reminders));
  }

  // Toggle reminder completion
  toggleReminder(id: number): ReminderItem[] {
    const reminders = this.getReminders();
    const updated = reminders.map(reminder => 
      reminder.id === id 
        ? { ...reminder, completed: !reminder.completed }
        : reminder
    );
    
    this.saveReminders(updated);
    
    // Award XP for completing reminders
    const completedReminder = updated.find(r => r.id === id);
    if (completedReminder?.completed) {
      const userData = this.getUserData();
      userData.xp += 25; // 25 XP for completing a reminder
      userData.level = Math.floor(userData.xp / 1000) + 1;
      this.saveUserData(userData);
    }
    
    return updated;
  }

  // Reset daily data (for testing)
  resetDailyData(): void {
    const data = this.getUserData();
    data.calories = 0;
    data.waterIntake = 0;
    data.lastActiveDate = new Date().toDateString();
    this.saveUserData(data);
    
    const workout = this.getWorkout();
    workout.completed = false;
    workout.completedDate = undefined;
    this.saveWorkout(workout);
    
    const reminders = this.getReminders();
    const resetReminders = reminders.map(r => ({ ...r, completed: false }));
    this.saveReminders(resetReminders);
  }
}

export const userStore = new UserStore();
export type { UserData, WorkoutData, FoodEntry, ReminderItem };
