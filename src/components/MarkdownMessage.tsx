import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github-dark.css';

interface MarkdownMessageProps {
  content: string;
  className?: string;
}

const MarkdownMessage = ({ content, className = '' }: MarkdownMessageProps) => {
  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Custom styling for markdown elements
          h1: ({ children }) => (
            <h1 className="text-xl font-bold text-white mb-3 border-b border-white/20 pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-lg font-bold text-white mb-2">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-base font-bold text-white mb-2">
              {children}
            </h3>
          ),
          p: ({ children }) => (
            <p className="text-white mb-2 leading-relaxed">
              {children}
            </p>
          ),
          strong: ({ children }) => (
            <strong className="font-bold text-neon-blue">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="italic text-neon-purple">
              {children}
            </em>
          ),
          ul: ({ children }) => (
            <ul className="list-none space-y-1 mb-3 ml-2">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-1 mb-3 ml-2 text-white">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-white flex items-start">
              <span className="text-neon-blue mr-2 mt-1">•</span>
              <span className="flex-1">{children}</span>
            </li>
          ),
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-neon-blue pl-4 py-2 bg-glass/30 rounded-r-lg mb-3">
              {children}
            </blockquote>
          ),
          code: ({ inline, children }) => {
            if (inline) {
              return (
                <code className="bg-glass/50 text-neon-blue px-1.5 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              );
            }
            return (
              <code className="block bg-neumorphic-dark p-3 rounded-lg text-sm font-mono text-white overflow-x-auto mb-3">
                {children}
              </code>
            );
          },
          pre: ({ children }) => (
            <pre className="bg-neumorphic-dark p-3 rounded-lg text-sm font-mono text-white overflow-x-auto mb-3 border border-white/10">
              {children}
            </pre>
          ),
          a: ({ href, children }) => (
            <a 
              href={href} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-neon-blue hover:text-neon-purple transition-colors underline"
            >
              {children}
            </a>
          ),
          table: ({ children }) => (
            <div className="overflow-x-auto mb-3">
              <table className="min-w-full border border-white/20 rounded-lg">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-white/20 px-3 py-2 bg-glass/30 text-white font-bold text-left">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-white/20 px-3 py-2 text-white">
              {children}
            </td>
          ),
          hr: () => (
            <hr className="border-white/20 my-4" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownMessage;
