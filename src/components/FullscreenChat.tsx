import { useState } from "react";
import ChatBot from "./ChatBot";
import { type UserData } from "@/services/aiService";

interface FullscreenChatProps {
  userData?: UserData;
  onClose: () => void;
}

const FullscreenChat = ({ userData, onClose }: FullscreenChatProps) => {
  const [isFullscreen, setIsFullscreen] = useState(true);

  const handleToggleFullscreen = () => {
    if (isFullscreen) {
      onClose();
    }
    setIsFullscreen(!isFullscreen);
  };

  return (
    <ChatBot 
      userData={userData}
      isFullscreen={isFullscreen}
      onToggleFullscreen={handleToggleFullscreen}
    />
  );
};

export default FullscreenChat;
