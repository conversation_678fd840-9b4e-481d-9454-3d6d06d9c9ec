
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, Trash2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

const TaskComponent = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [newTask, setNewTask] = useState("");

  // Load tasks from localStorage on component mount
  useEffect(() => {
    const savedTasks = localStorage.getItem("vitalEdgeTasks");
    if (savedTasks) {
      setTasks(JSON.parse(savedTasks));
    }
  }, []);

  // Save tasks to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem("vitalEdgeTasks", JSON.stringify(tasks));
  }, [tasks]);

  const addTask = () => {
    if (newTask.trim() === "") {
      toast({
        title: "Task cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const task: Task = {
      id: Date.now().toString(),
      title: newTask,
      completed: false,
      createdAt: new Date().toISOString(),
    };

    setTasks([...tasks, task]);
    setNewTask("");
    toast({
      title: "Task added",
      description: "Your task has been added successfully.",
    });
  };

  const toggleComplete = (id: string) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const deleteTask = (id: string) => {
    setTasks(tasks.filter((task) => task.id !== id));
    toast({
      title: "Task deleted",
      description: "Your task has been deleted.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="card-neumorphic p-6 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-neon-purple/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>

        <div className="relative">
          <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-blue mb-6">Today's Tasks</h2>

          <div className="flex space-x-3 mb-6">
            <Input
              value={newTask}
              onChange={(e) => setNewTask(e.target.value)}
              placeholder="Add a new task..."
              className="bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white placeholder:text-white/60 focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
              onKeyDown={(e) => e.key === "Enter" && addTask()}
            />
            <Button
              onClick={addTask}
              className="btn-glow-blue text-white font-medium px-6"
            >
              Add
            </Button>
          </div>

          <div className="space-y-3 max-h-[400px] overflow-y-auto">
            {tasks.length === 0 ? (
              <div className="card-glass p-6 text-center">
                <p className="text-white/70">No tasks for today. Add some!</p>
              </div>
            ) : (
              tasks.map((task) => (
                <div
                  key={task.id}
                  className={`card-glass p-4 flex items-center justify-between transition-all duration-300 ${
                    task.completed ? "opacity-70" : ""
                  }`}
                >
                  <div className="flex items-center">
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => toggleComplete(task.id)}
                      className={`h-8 w-8 rounded-full mr-3 transition-all duration-300 ${
                        task.completed
                          ? "bg-gradient-blue text-white shadow-glow-blue"
                          : "bg-glass/50 border border-neon-blue/20 text-white hover:border-neon-blue/40"
                      }`}
                    >
                      {task.completed && <Check className="h-4 w-4" />}
                    </Button>
                    <span
                      className={`text-sm font-medium ${
                        task.completed ? "line-through text-white/50" : "text-white"
                      }`}
                    >
                      {task.title}
                    </span>
                  </div>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => deleteTask(task.id)}
                    className="h-8 w-8 bg-glass/50 border border-red-500/20 text-red-400 hover:border-red-500/40 hover:text-red-300 transition-all duration-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskComponent;
