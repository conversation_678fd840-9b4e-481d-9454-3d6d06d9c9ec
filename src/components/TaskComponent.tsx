
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, Trash2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

const TaskComponent = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [newTask, setNewTask] = useState("");

  // Load tasks from localStorage on component mount
  useEffect(() => {
    const savedTasks = localStorage.getItem("vitalEdgeTasks");
    if (savedTasks) {
      setTasks(JSON.parse(savedTasks));
    }
  }, []);

  // Save tasks to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem("vitalEdgeTasks", JSON.stringify(tasks));
  }, [tasks]);

  const addTask = () => {
    if (newTask.trim() === "") {
      toast({
        title: "Task cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const task: Task = {
      id: Date.now().toString(),
      title: newTask,
      completed: false,
      createdAt: new Date().toISOString(),
    };

    setTasks([...tasks, task]);
    setNewTask("");
    toast({
      title: "Task added",
      description: "Your task has been added successfully.",
    });
  };

  const toggleComplete = (id: string) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const deleteTask = (id: string) => {
    setTasks(tasks.filter((task) => task.id !== id));
    toast({
      title: "Task deleted",
      description: "Your task has been deleted.",
    });
  };

  return (
    <div className="bg-vital-gray-dark rounded-lg p-5 w-full h-full">
      <h2 className="text-xl font-bold text-white mb-4">Today's Tasks</h2>
      <div className="flex space-x-2 mb-6">
        <Input
          value={newTask}
          onChange={(e) => setNewTask(e.target.value)}
          placeholder="Add a new task..."
          className="bg-vital-gray-medium text-white"
          onKeyDown={(e) => e.key === "Enter" && addTask()}
        />
        <Button
          onClick={addTask}
          className="bg-vital-lime text-black hover:bg-vital-lime-bright"
        >
          Add
        </Button>
      </div>

      <div className="space-y-3 max-h-[400px] overflow-y-auto">
        {tasks.length === 0 ? (
          <p className="text-white text-center py-4">No tasks for today. Add some!</p>
        ) : (
          tasks.map((task) => (
            <div
              key={task.id}
              className={`flex items-center justify-between p-3 rounded-md ${
                task.completed ? "bg-green-900/20" : "bg-vital-gray-medium"
              }`}
            >
              <div className="flex items-center">
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => toggleComplete(task.id)}
                  className={`h-6 w-6 rounded-full mr-2 ${
                    task.completed
                      ? "bg-vital-lime text-black"
                      : "text-white"
                  }`}
                >
                  {task.completed && <Check className="h-4 w-4" />}
                </Button>
                <span
                  className={`text-sm ${
                    task.completed ? "line-through text-gray-400" : "text-white"
                  }`}
                >
                  {task.title}
                </span>
              </div>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => deleteTask(task.id)}
                className="h-6 w-6 text-white hover:text-red-400"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TaskComponent;
