import { useState } from 'react';
import { Progress } from "@/components/ui/progress";

interface UserProfileProps {
  name?: string;
  level?: number;
  xp?: number;
  streak?: number;
  goals?: {
    completed: number;
    total: number;
  };
}

const UserProfile = ({
  name = "<PERSON>",
  level = 12,
  xp = 2850,
  streak = 7,
  goals = { completed: 3, total: 5 }
}: UserProfileProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Calculate XP progress to next level (assuming 1000 XP per level)
  const xpProgress = (xp % 1000) / 10;
  const xpToNextLevel = 1000 - (xp % 1000);

  return (
    <div className="card-glass">
      <div className="p-5">
        <div className="flex items-center">
          <div className="relative">
            <div className="w-16 h-16 rounded-full bg-gradient-blue flex items-center justify-center text-white font-bold text-xl">
              {name.charAt(0)}
            </div>
            <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-gradient-purple flex items-center justify-center text-white text-xs font-bold">
              {level}
            </div>
          </div>

          <div className="ml-4 flex-1">
            <h3 className="text-white font-bold">{name}</h3>
            <div className="flex items-center text-white/70 text-sm">
              <span>Level {level}</span>
              <span className="mx-2">•</span>
              <span>{xp} XP</span>
            </div>
            <div className="mt-1">
              <Progress
                value={xpProgress}
                className="h-1.5 bg-neumorphic-dark overflow-hidden rounded-full"
                indicatorClassName="bg-gradient-purple"
              />
              <div className="text-white/60 text-xs mt-1">{xpToNextLevel} XP to Level {level + 1}</div>
            </div>
          </div>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-white hover:text-neon-blue transition-colors"
          >
            <svg
              className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-white/10 grid grid-cols-2 gap-4">
            <div className="bg-glass/50 rounded-lg p-3">
              <div className="text-white/70 text-xs mb-1">Current Streak</div>
              <div className="flex items-center">
                <span className="text-white font-bold text-lg">{streak}</span>
                <span className="text-white/70 text-sm ml-1">days</span>
                <span className="ml-2">🔥</span>
              </div>
            </div>

            <div className="bg-glass/50 rounded-lg p-3">
              <div className="text-white/70 text-xs mb-1">Today's Goals</div>
              <div className="flex items-center">
                <span className="text-white font-bold text-lg">{goals.completed}/{goals.total}</span>
                <span className="text-white/70 text-sm ml-1">completed</span>
              </div>
            </div>

            <div className="col-span-2">
              <button className="w-full py-2 rounded-lg bg-glass/50 border border-neon-blue/20 text-white hover:border-neon-blue/40 transition-all duration-300 text-sm">
                View Full Profile
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserProfile;
