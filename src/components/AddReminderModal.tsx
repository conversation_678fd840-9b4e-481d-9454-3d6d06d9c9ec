import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Plus } from "lucide-react";
import { type ReminderItem } from "@/store/userStore";

interface AddReminderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (text: string, time: string, type: ReminderItem['type'], days: string[]) => void;
}

const AddReminderModal = ({ isOpen, onClose, onAdd }: AddReminderModalProps) => {
  const [text, setText] = useState("");
  const [time, setTime] = useState("");
  const [type, setType] = useState<ReminderItem['type']>("general");
  const [selectedDays, setSelectedDays] = useState<string[]>(["daily"]);

  const days = [
    { key: "daily", label: "Daily" },
    { key: "monday", label: "Mon" },
    { key: "tuesday", label: "Tue" },
    { key: "wednesday", label: "Wed" },
    { key: "thursday", label: "Thu" },
    { key: "friday", label: "Fri" },
    { key: "saturday", label: "Sat" },
    { key: "sunday", label: "Sun" }
  ];

  const types = [
    { key: "general", label: "General", icon: "⏰" },
    { key: "workout", label: "Workout", icon: "💪" },
    { key: "nutrition", label: "Nutrition", icon: "🥗" },
    { key: "hydration", label: "Hydration", icon: "💧" }
  ];

  const handleDayToggle = (day: string) => {
    if (day === "daily") {
      setSelectedDays(["daily"]);
    } else {
      const newDays = selectedDays.includes("daily") 
        ? [day]
        : selectedDays.includes(day)
          ? selectedDays.filter(d => d !== day)
          : [...selectedDays.filter(d => d !== "daily"), day];
      
      setSelectedDays(newDays.length === 0 ? ["daily"] : newDays);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim() && time) {
      onAdd(text.trim(), time, type, selectedDays);
      setText("");
      setTime("");
      setType("general");
      setSelectedDays(["daily"]);
      onClose();
    }
  };

  const handleClose = () => {
    setText("");
    setTime("");
    setType("general");
    setSelectedDays(["daily"]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50">
      <div className="card-neumorphic w-full max-w-md animate-scale-in p-6 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-neon-purple/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>
        
        {/* Header */}
        <div className="flex justify-between items-center mb-6 relative">
          <h3 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-blue">Add New Reminder</h3>
          <button
            onClick={handleClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5 relative">
          {/* Reminder Text */}
          <div>
            <label className="text-white block mb-2 font-medium">Reminder Text</label>
            <Input
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="e.g., Take vitamins"
              className="bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white placeholder:text-white/60 focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
              required
            />
          </div>

          {/* Time */}
          <div>
            <label className="text-white block mb-2 font-medium">Time</label>
            <Input
              type="time"
              value={time}
              onChange={(e) => setTime(e.target.value)}
              className="bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
              required
            />
          </div>

          {/* Type */}
          <div>
            <label className="text-white block mb-2 font-medium">Type</label>
            <div className="grid grid-cols-2 gap-2">
              {types.map((typeOption) => (
                <button
                  key={typeOption.key}
                  type="button"
                  onClick={() => setType(typeOption.key as ReminderItem['type'])}
                  className={`p-3 rounded-lg border transition-all duration-300 flex items-center gap-2 ${
                    type === typeOption.key
                      ? 'bg-neon-blue/20 border-neon-blue/40 text-white'
                      : 'bg-glass/30 border-white/20 text-white/70 hover:border-white/40'
                  }`}
                >
                  <span>{typeOption.icon}</span>
                  <span className="text-sm">{typeOption.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Days */}
          <div>
            <label className="text-white block mb-2 font-medium">Repeat</label>
            <div className="grid grid-cols-4 gap-2">
              {days.map((day) => (
                <button
                  key={day.key}
                  type="button"
                  onClick={() => handleDayToggle(day.key)}
                  className={`p-2 rounded-lg border transition-all duration-300 text-xs ${
                    selectedDays.includes(day.key)
                      ? 'bg-neon-purple/20 border-neon-purple/40 text-white'
                      : 'bg-glass/30 border-white/20 text-white/70 hover:border-white/40'
                  }`}
                >
                  {day.label}
                </button>
              ))}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-2">
            <Button
              type="button"
              onClick={handleClose}
              variant="outline"
              className="flex-1 bg-glass/50 border-white/20 text-white hover:border-white/40"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 btn-glow-blue text-white"
              disabled={!text.trim() || !time}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Reminder
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddReminderModal;
