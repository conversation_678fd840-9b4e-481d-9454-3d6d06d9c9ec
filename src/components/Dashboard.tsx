
import { useState, useEffect } from "react";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { SidebarInset } from "@/components/ui/sidebar";
import { toast } from "@/hooks/use-toast";
import AppSidebar from "./AppSidebar";
import TaskComponent from "./TaskComponent";
import ChatBot from "./ChatBot";
import FullscreenChat from "./FullscreenChat";
import CalendarWidget from "./CalendarWidget";
import UserProfile from "./UserProfile";
import NotesWidget from "./NotesWidget";
import AddReminderModal from "./AddReminderModal";
import { userStore, type UserData as StoreUserData, type WorkoutData, type ReminderItem, type StatisticsData } from "@/store/userStore";
import { notificationService } from "@/services/notificationService";

interface DashboardProps {
  onNavigate: (page: string) => void;
}

// Use types from the store
type LocalUserData = StoreUserData & { notes: string };

const Dashboard = ({ onNavigate }: DashboardProps) => {
  // Real user data from store
  const [userData, setUserData] = useState<StoreUserData>(() => userStore.getUserData());
  const [workout, setWorkout] = useState<WorkoutData>(() => userStore.getWorkout());
  const [reminders, setReminders] = useState<ReminderItem[]>(() => userStore.getReminders());
  const [statistics, setStatistics] = useState<StatisticsData>(() => userStore.getStatistics());
  const [notes, setNotes] = useState<string>(() => {
    const saved = localStorage.getItem("vitalEdgeNotes");
    return saved || "";
  });

  const [newFoodName, setNewFoodName] = useState("");
  const [newFoodCalories, setNewFoodCalories] = useState("");
  const [showAddFood, setShowAddFood] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [showFullscreenChat, setShowFullscreenChat] = useState(false);
  const [showAddReminder, setShowAddReminder] = useState(false);

  // Destructure userData for easier use
  const { calories, waterIntake, streak, xp, level, dailyGoal, waterGoal } = userData;

  // Save user data to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("vitalEdgeUserData", JSON.stringify(userData));
  }, [userData]);

  // Save reminders to localStorage when they change
  useEffect(() => {
    localStorage.setItem("vitalEdgeReminders", JSON.stringify(reminders));
  }, [reminders]);

  const progressPercentage = (calories / dailyGoal) * 100;
  const waterProgressPercentage = (waterIntake / waterGoal) * 100;

  // Initialize notification service
  useEffect(() => {
    // Request notification permission and start checking
    notificationService.requestPermission().then(granted => {
      if (granted) {
        console.log('✅ Notifications enabled');
      } else {
        console.log('❌ Notifications disabled');
      }
    });

    // Cleanup on unmount
    return () => {
      // Don't stop the service on unmount as we want it to run globally
    };
  }, []);

  const handleAddReminder = (text: string, time: string, type: ReminderItem['type'], days: string[]) => {
    const newReminder = userStore.addReminder(text, time, type, days);
    const updatedReminders = userStore.getReminders();
    setReminders(updatedReminders);

    toast({
      title: "Reminder Added! ⏰",
      description: `"${text}" has been scheduled for ${time}`,
    });
  };

  const handleAddFood = () => {
    if (newFoodName && newFoodCalories) {
      userStore.addFoodEntry(newFoodName, parseInt(newFoodCalories));
      const updatedUserData = userStore.getUserData();
      setUserData(updatedUserData);

      setNewFoodName("");
      setNewFoodCalories("");
      setShowAddFood(false);
      toast({
        title: "Food Added!",
        description: `${newFoodName} (${newFoodCalories} cal) has been added to your daily intake.`,
      });
    }
  };

  const handleAddWater = () => {
    const updatedUserData = userStore.addWater(0.25);
    setUserData(updatedUserData);
    toast({
      title: "Great hydration! 💧",
      description: "250ml added to your daily intake.",
    });
  };

  const handleWorkoutComplete = () => {
    const updatedUserData = userStore.completeWorkout();
    const updatedWorkout = userStore.getWorkout();
    setUserData(updatedUserData);
    setWorkout(updatedWorkout);
    toast({
      title: "Workout Complete! 🎉",
      description: `You've earned 100 XP and extended your streak to ${updatedUserData.streak} days!`,
    });
  };

  const markReminderDone = (id: number) => {
    const updatedReminders = userStore.toggleReminder(id);
    const updatedUserData = userStore.getUserData();
    setReminders(updatedReminders);
    setUserData(updatedUserData);

    toast({
      title: "Reminder Completed ✅",
      description: "Great job staying on track! +25 XP",
    });
  };

  const renderContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* User Profile */}
            <UserProfile
              name="Alex Johnson"
              level={level}
              xp={xp}
              streak={streak}
              goals={{ completed: 3, total: 5 }}
            />

            <div className="grid md:grid-cols-2 gap-6">
              {/* Today's Workout */}
              <div className="card-neumorphic animate-fade-in p-5 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
                <div className="mb-4 relative">
                  <h3 className="text-lg font-bold text-neon-blue">Today's Workout</h3>
                </div>
                <div className="relative">
                  <h3 className="text-xl font-bold text-white mb-2">{workout.name}</h3>
                  <p className="text-white/70 mb-4">{workout.duration}</p>
                  <div className="space-y-3 mb-5">
                    {workout.exercises.map((exercise, index) => (
                      <div key={index} className="flex items-center space-x-3 p-2 rounded-lg bg-glass/50 backdrop-blur-sm border border-white/5">
                        <div className="w-2 h-2 bg-neon-blue rounded-full"></div>
                        <span className="text-white">{exercise}</span>
                      </div>
                    ))}
                  </div>
                  <button
                    onClick={handleWorkoutComplete}
                    className="w-full py-3 rounded-lg btn-glow-blue text-white font-medium"
                  >
                    Complete Workout
                  </button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="card-glass animate-fade-in p-5 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 bg-neon-purple/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-neon-blue/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>
                <div className="mb-4 relative">
                  <h3 className="text-lg font-bold text-neon-purple">Quick Actions</h3>
                </div>
                <div className="space-y-3 relative">
                  <button
                    onClick={handleAddWater}
                    className="w-full p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white hover:border-neon-blue/40 transition-all duration-300"
                  >
                    💧 Add 250ml Water
                  </button>
                  <button
                    onClick={() => setShowAddFood(true)}
                    className="w-full p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white hover:border-neon-blue/40 transition-all duration-300"
                  >
                    🍎 Log Food Intake
                  </button>
                  <button
                    onClick={handleWorkoutComplete}
                    className="w-full p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-purple/20 text-white hover:border-neon-purple/40 transition-all duration-300"
                  >
                    ✅ Mark Workout Complete
                  </button>
                  <button
                    onClick={() => onNavigate('generate')}
                    className="w-full py-3 rounded-lg btn-glow-purple text-white font-medium"
                  >
                    🔄 Generate New Plan
                  </button>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <CalendarWidget />
              <NotesWidget />
            </div>
          </div>
        );
      case "tasks":
        return <TaskComponent />;
      case "calendar":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-blue mb-4">Calendar</h2>
            <div className="grid grid-cols-1 gap-6">
              <CalendarWidget />
              <div className="card-glass p-5">
                <h3 className="text-lg font-bold text-neon-blue mb-4">Today's Schedule</h3>
                <div className="space-y-3">
                  {reminders.filter(r => r.enabled).map((reminder) => {
                    const iconMap = {
                      workout: '💪',
                      nutrition: '🥗',
                      hydration: '💧',
                      general: '⏰'
                    };

                    const colorMap = {
                      workout: 'border-neon-blue/20',
                      nutrition: 'border-neon-purple/20',
                      hydration: 'border-neon-blue/20',
                      general: 'border-white/20'
                    };

                    return (
                      <div key={reminder.id} className={`p-3 rounded-lg bg-glass/50 ${colorMap[reminder.type]}`}>
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-3">
                            <span className="text-lg">{iconMap[reminder.type]}</span>
                            <div>
                              <h4 className={`font-medium ${reminder.completed ? 'text-white/50 line-through' : 'text-white'}`}>
                                {reminder.text}
                              </h4>
                              <p className="text-white/70 text-sm">
                                {reminder.days.includes('daily') ? 'Daily' : reminder.days.join(', ')}
                              </p>
                            </div>
                          </div>
                          <div className="text-white/70 text-sm">
                            {reminder.time}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {reminders.filter(r => r.enabled).length === 0 && (
                    <div className="text-center py-8 text-white/60">
                      <p>No reminders scheduled for today</p>
                      <button
                        onClick={() => setShowAddReminder(true)}
                        className="mt-2 text-neon-blue hover:text-neon-purple transition-colors"
                      >
                        Add your first reminder
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case "reminders":
        return (
          <div className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-purple">Today's Reminders</h2>
              <button
                onClick={() => setShowAddReminder(true)}
                className="btn-glow-purple text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <span className="text-lg">+</span>
                Add Reminder
              </button>
            </div>
            {reminders.map((reminder) => (
              <div
                key={reminder.id}
                className={`card-glass animate-slide-up ${
                  reminder.completed ? 'opacity-70' : ''
                }`}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        reminder.type === 'hydration' ? 'bg-neon-blue' :
                        reminder.type === 'nutrition' ? 'bg-neon-purple' :
                        'bg-gradient-blue'
                      }`}></div>
                      <div>
                        <p className={`font-medium ${
                          reminder.completed ? 'text-white/50 line-through' : 'text-white'
                        }`}>{reminder.text}</p>
                        <p className="text-white/60 text-sm">{reminder.time}</p>
                      </div>
                    </div>
                    {!reminder.completed && (
                      <button
                        onClick={() => markReminderDone(reminder.id)}
                        className="px-3 py-1 rounded-full bg-glass/50 border border-neon-blue/20 text-white hover:border-neon-blue/40 transition-all duration-300 text-sm"
                      >
                        Done
                      </button>
                    )}
                    {reminder.completed && (
                      <span className="text-neon-blue text-sm font-medium flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        Completed
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      case "chat":
        return (
          <ChatBot
            userData={{
              calories,
              waterIntake,
              streak,
              xp,
              level,
              dailyGoal,
              waterGoal
            }}
            onToggleFullscreen={() => setShowFullscreenChat(true)}
          />
        );
      case "stats":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-blue mb-4">Statistics</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="card-neumorphic p-5">
                <h3 className="text-lg font-bold text-neon-blue mb-4">Weekly Progress</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-white text-sm">Workouts</span>
                      <span className="text-white text-sm">{statistics.weeklyWorkouts}/7</span>
                    </div>
                    <Progress value={(statistics.weeklyWorkouts / 7) * 100} className="h-1.5 bg-neumorphic-dark overflow-hidden rounded-full" indicatorClassName="bg-gradient-blue" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-white text-sm">Nutrition</span>
                      <span className="text-white text-sm">{statistics.weeklyNutritionGoals}/7</span>
                    </div>
                    <Progress value={(statistics.weeklyNutritionGoals / 7) * 100} className="h-1.5 bg-neumorphic-dark overflow-hidden rounded-full" indicatorClassName="bg-gradient-purple" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-white text-sm">Hydration</span>
                      <span className="text-white text-sm">{statistics.weeklyHydrationGoals}/7</span>
                    </div>
                    <Progress value={(statistics.weeklyHydrationGoals / 7) * 100} className="h-1.5 bg-neumorphic-dark overflow-hidden rounded-full" indicatorClassName="bg-gradient-blue" />
                  </div>
                </div>
              </div>

              <div className="card-glass p-5 md:col-span-2">
                <h3 className="text-lg font-bold text-neon-purple mb-4">Monthly Overview</h3>
                <div className="grid grid-cols-4 gap-4">
                  <div className="bg-glass/50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-white">{statistics.monthlyWorkouts}</div>
                    <div className="text-white/70 text-sm">Workouts</div>
                  </div>
                  <div className="bg-glass/50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-white">{Math.round((statistics.weeklyNutritionGoals / 7) * 100)}%</div>
                    <div className="text-white/70 text-sm">Nutrition</div>
                  </div>
                  <div className="bg-glass/50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-white">{Math.round((statistics.weeklyHydrationGoals / 7) * 100)}%</div>
                    <div className="text-white/70 text-sm">Hydration</div>
                  </div>
                  <div className="bg-glass/50 rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-white">{(statistics.monthlyXP / 1000).toFixed(1)}k</div>
                    <div className="text-white/70 text-sm">XP Earned</div>
                  </div>
                </div>

                <div className="mt-6 h-40 flex items-end justify-between px-2">
                  {statistics.weeklyProgress.map((value, index) => (
                    <div key={index} className="flex flex-col items-center">
                      <div
                        className="w-8 bg-gradient-blue rounded-t-md"
                        style={{ height: `${Math.min(value, 100)}%` }}
                      ></div>
                      <div className="text-white/70 text-xs mt-2">
                        {['M', 'T', 'W', 'T', 'F', 'S', 'S'][index]}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="card-neumorphic p-5">
              <h3 className="text-lg font-bold text-neon-blue mb-4">Achievement Progress</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`bg-glass/50 rounded-lg p-3 flex items-center space-x-3 ${statistics.achievements.sevenDayStreak ? '' : 'opacity-50'}`}>
                  <div className="w-10 h-10 rounded-full bg-gradient-blue flex items-center justify-center text-white">
                    🔥
                  </div>
                  <div>
                    <div className="text-white text-sm font-medium">7-Day Streak</div>
                    <div className="text-white/70 text-xs">{statistics.achievements.sevenDayStreak ? 'Completed' : `${streak}/7 days`}</div>
                  </div>
                </div>
                <div className={`bg-glass/50 rounded-lg p-3 flex items-center space-x-3 ${statistics.achievements.twentyWorkouts ? '' : 'opacity-50'}`}>
                  <div className="w-10 h-10 rounded-full bg-gradient-purple flex items-center justify-center text-white">
                    💪
                  </div>
                  <div>
                    <div className="text-white text-sm font-medium">20 Workouts</div>
                    <div className="text-white/70 text-xs">{statistics.monthlyWorkouts}/20 {statistics.achievements.twentyWorkouts ? '• Completed' : ''}</div>
                  </div>
                </div>
                <div className={`bg-glass/50 rounded-lg p-3 flex items-center space-x-3 ${statistics.achievements.hydrationMaster >= 30 ? '' : 'opacity-50'}`}>
                  <div className="w-10 h-10 rounded-full bg-gradient-blue flex items-center justify-center text-white">
                    💧
                  </div>
                  <div>
                    <div className="text-white text-sm font-medium">Hydration Master</div>
                    <div className="text-white/70 text-xs">{statistics.achievements.hydrationMaster}/30 days</div>
                  </div>
                </div>
                <div className={`bg-glass/50 rounded-lg p-3 flex items-center space-x-3 ${statistics.achievements.nutritionPro >= 30 ? '' : 'opacity-50'}`}>
                  <div className="w-10 h-10 rounded-full bg-gradient-purple flex items-center justify-center text-white">
                    🥗
                  </div>
                  <div>
                    <div className="text-white text-sm font-medium">Nutrition Pro</div>
                    <div className="text-white/70 text-xs">{statistics.achievements.nutritionPro}/30 days</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return <div className="text-white text-center p-10">Coming Soon</div>;
    }
  };

  return (
    <>
      <AppSidebar
        onNavigate={onNavigate}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        streak={streak}
        xp={xp}
      />

      <SidebarInset className="h-screen bg-gradient-background text-white flex flex-col">
        {/* Header */}
        <div className="backdrop-blur-md bg-glass/80 p-4 border-b border-white/5 relative overflow-hidden flex-shrink-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-neon-blue/5 to-neon-purple/5 pointer-events-none"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
          <div className="container mx-auto flex justify-between items-center relative">
            <div className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-blue">
              {activeTab === "overview" ? "Dashboard" :
               activeTab === "tasks" ? "Tasks" :
               activeTab === "reminders" ? "Reminders" :
               activeTab === "chat" ? "Assistant" :
               activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </div>
            <div className="flex items-center space-x-4">
              <Badge className="bg-gradient-blue text-white rounded-full px-3 py-1 shadow-glow-blue">
                🔥 {streak} day streak
              </Badge>
              <Badge className="bg-gradient-purple text-white rounded-full px-3 py-1 shadow-glow-purple">
                ⭐ {xp} XP
              </Badge>
            </div>
          </div>
        </div>

        <div className={`container mx-auto flex-1 flex flex-col ${activeTab === "chat" ? "p-0" : "p-6 overflow-y-auto"}`}>
          {/* Stats Overview */}
          {activeTab === "overview" && (
            <div className="grid md:grid-cols-4 gap-6 mb-8">
              <div className="card-glass animate-scale-in relative overflow-hidden">
                <div className="absolute top-0 left-0 w-20 h-20 bg-neon-blue/10 rounded-full blur-3xl -ml-10 -mt-10 pointer-events-none"></div>
                <div className="p-4 pb-2 relative">
                  <div className="text-neon-blue text-sm font-semibold">Calories</div>
                </div>
                <div className="p-4 pt-0 relative">
                  <div className="text-2xl font-bold text-white">{calories}</div>
                  <div className="text-sm text-white/80">of {dailyGoal} goal</div>
                  <Progress
                    value={progressPercentage}
                    className="mt-2 h-1.5 bg-neumorphic-dark overflow-hidden rounded-full"
                    indicatorClassName="bg-gradient-blue"
                  />
                </div>
              </div>

              <div className="card-glass animate-scale-in relative overflow-hidden" style={{ animationDelay: '0.1s' }}>
                <div className="absolute top-0 right-0 w-20 h-20 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
                <div className="p-4 pb-2 relative">
                  <div className="text-neon-blue text-sm font-semibold">Hydration</div>
                </div>
                <div className="p-4 pt-0 relative">
                  <div className="text-2xl font-bold text-white">{waterIntake}L</div>
                  <div className="text-sm text-white/80">of {waterGoal}L goal</div>
                  <Progress
                    value={waterProgressPercentage}
                    className="mt-2 h-1.5 bg-neumorphic-dark overflow-hidden rounded-full"
                    indicatorClassName="bg-gradient-blue"
                  />
                </div>
              </div>

              <div className="card-glass animate-scale-in relative overflow-hidden" style={{ animationDelay: '0.2s' }}>
                <div className="absolute bottom-0 left-0 w-20 h-20 bg-neon-purple/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>
                <div className="p-4 pb-2 relative">
                  <div className="text-neon-purple text-sm font-semibold">Streak</div>
                </div>
                <div className="p-4 pt-0 relative">
                  <div className="text-2xl font-bold text-white">{streak} days</div>
                  <div className="text-sm text-white/80">Keep it going!</div>
                  <div className="mt-2 flex space-x-1">
                    <span className="text-lg animate-pulse" style={{ animationDelay: '0s' }}>🔥</span>
                    <span className="text-lg animate-pulse" style={{ animationDelay: '0.3s' }}>🔥</span>
                    <span className="text-lg animate-pulse" style={{ animationDelay: '0.6s' }}>🔥</span>
                  </div>
                </div>
              </div>

              <div className="card-glass animate-scale-in relative overflow-hidden" style={{ animationDelay: '0.3s' }}>
                <div className="absolute bottom-0 right-0 w-20 h-20 bg-neon-purple/10 rounded-full blur-3xl -mr-10 -mb-10 pointer-events-none"></div>
                <div className="p-4 pb-2 relative">
                  <div className="text-neon-purple text-sm font-semibold">Experience</div>
                </div>
                <div className="p-4 pt-0 relative">
                  <div className="text-2xl font-bold text-white">{xp} XP</div>
                  <div className="text-sm text-white/80">Level {level}</div>
                  <Progress
                    value={(xp % 1000) / 10}
                    className="mt-2 h-1.5 bg-neumorphic-dark overflow-hidden rounded-full"
                    indicatorClassName="bg-gradient-purple"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className={activeTab === "chat" ? "flex-1 flex flex-col min-h-0" : "min-h-[500px]"}>
            {renderContent()}
          </div>

          {/* Add Food Modal */}
          {showAddFood && (
            <div className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50">
              <div className="card-neumorphic w-full max-w-md animate-scale-in p-6 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-40 h-40 bg-neon-blue/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-neon-purple/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>
                <div className="mb-4 relative">
                  <h3 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-blue">Add Food to Daily Intake</h3>
                </div>
                <div className="grid gap-5 relative">
                  <div>
                    <label className="text-white block mb-2 font-medium">Food Name</label>
                    <input
                      value={newFoodName}
                      onChange={(e) => setNewFoodName(e.target.value)}
                      className="w-full p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
                      placeholder="e.g., Grilled chicken breast"
                    />
                  </div>
                  <div>
                    <label className="text-white block mb-2 font-medium">Calories</label>
                    <input
                      type="number"
                      value={newFoodCalories}
                      onChange={(e) => setNewFoodCalories(e.target.value)}
                      className="w-full p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
                      placeholder="e.g., 250"
                    />
                  </div>
                  <div className="flex space-x-3 mt-2">
                    <button
                      onClick={() => setShowAddFood(false)}
                      className="w-1/2 p-3 rounded-lg bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white hover:border-neon-blue/40 transition-all duration-300"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAddFood}
                      className="w-1/2 py-3 rounded-lg btn-glow-blue text-white font-medium"
                    >
                      Add Food
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </SidebarInset>

      {/* Fullscreen Chat */}
      {showFullscreenChat && (
        <FullscreenChat
          userData={{
            calories,
            waterIntake,
            streak,
            xp,
            level,
            dailyGoal,
            waterGoal
          }}
          onClose={() => setShowFullscreenChat(false)}
        />
      )}

      {/* Add Reminder Modal */}
      <AddReminderModal
        isOpen={showAddReminder}
        onClose={() => setShowAddReminder(false)}
        onAdd={handleAddReminder}
      />
    </>
  );
};

export default Dashboard;
