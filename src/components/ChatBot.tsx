
import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowUp } from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: string;
}

const initMessages: Message[] = [
  {
    id: "1",
    content: "Hi there! I'm your Vita<PERSON><PERSON><PERSON> assistant. How can I help with your health and fitness goals today?",
    sender: "bot",
    timestamp: new Date().toISOString(),
  },
];

// Function to generate contextual responses based on user input
const generateResponse = (userMessage: string): string => {
  const lowerCaseMessage = userMessage.toLowerCase();

  // Workout related questions
  if (lowerCaseMessage.includes("workout") || lowerCaseMessage.includes("exercise") || lowerCaseMessage.includes("training")) {
    if (lowerCaseMessage.includes("best") || lowerCaseMessage.includes("recommend")) {
      return "The best workout is one you'll stick with consistently. For overall fitness, I recommend a mix of strength training (2-3 days/week), cardio (2-3 days/week), and flexibility work. Your current plan includes upper body strength training which is excellent for building muscle and strength.";
    } else if (lowerCaseMessage.includes("often") || lowerCaseMessage.includes("frequency")) {
      return "For optimal results, aim to exercise 4-5 days per week with proper rest days in between. Your current streak of 7 days is impressive! Just make sure you're alternating muscle groups to allow for recovery.";
    } else {
      return "Regular exercise is crucial for both physical and mental health. Your current workout plan is well-structured. Remember to focus on proper form and gradually increase intensity as you progress.";
    }
  }

  // Nutrition related questions
  else if (lowerCaseMessage.includes("food") || lowerCaseMessage.includes("diet") || lowerCaseMessage.includes("nutrition") || lowerCaseMessage.includes("eat")) {
    if (lowerCaseMessage.includes("protein")) {
      return "Protein is essential for muscle recovery and growth. Aim for 1.6-2.2g per kg of bodyweight daily. Good sources include lean meats, fish, eggs, dairy, legumes, and plant-based options like tofu and tempeh.";
    } else if (lowerCaseMessage.includes("calorie") || lowerCaseMessage.includes("calories")) {
      return "Your current calorie intake is 1245 out of your 2200 daily goal. For weight maintenance, consume calories equal to your TDEE (Total Daily Energy Expenditure). For weight loss, a moderate deficit of 300-500 calories is sustainable.";
    } else {
      return "A balanced diet should include plenty of vegetables, fruits, lean proteins, healthy fats, and complex carbohydrates. Try to minimize processed foods and focus on whole, nutrient-dense options.";
    }
  }

  // Hydration related questions
  else if (lowerCaseMessage.includes("water") || lowerCaseMessage.includes("hydration") || lowerCaseMessage.includes("drink")) {
    return "Staying hydrated is crucial for performance and recovery. You've consumed 2.1L out of your 3L daily goal. Keep using the water tracking feature to ensure you're getting enough fluids throughout the day.";
  }

  // Default responses for other topics
  else {
    const defaultResponses = [
      "I'm here to help with your fitness and nutrition goals. Your current streak is 7 days - keep up the great work!",
      "Consistency is key to achieving your health goals. You're making excellent progress so far.",
      "Remember that rest and recovery are just as important as your workouts. Make sure you're getting enough sleep and managing stress.",
      "Your current stats show good progress. Is there anything specific about your fitness plan you'd like to discuss?",
      "I notice you've earned 2850 XP so far. Great job staying committed to your health journey!"
    ];
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }
};

const ChatBot = () => {
  const [messages, setMessages] = useState<Message[]>(() => {
    const savedMessages = localStorage.getItem("vitalEdgeChatMessages");
    return savedMessages ? JSON.parse(savedMessages) : initMessages;
  });
  const [inputValue, setInputValue] = useState("");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    localStorage.setItem("vitalEdgeChatMessages", JSON.stringify(messages));
    // Scroll to bottom when messages change
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const userInput = inputValue;
    setInputValue("");

    // Simulate bot response (with a small delay)
    setTimeout(() => {
      // Generate contextual response based on user input
      const responseContent = generateResponse(userInput);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: responseContent,
        sender: "bot",
        timestamp: new Date().toISOString(),
      };

      setMessages((prev) => [...prev, botMessage]);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="card-neumorphic overflow-hidden flex flex-col h-[600px] relative">
        <div className="absolute top-0 right-0 w-40 h-40 bg-neon-purple/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-neon-blue/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>

        {/* Header */}
        <div className="px-6 py-4 card-glass border-b border-white/5 relative">
          <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-purple">VitalEdge Assistant</h2>
          <p className="text-white/70 text-sm">Ask me anything about fitness and health</p>
        </div>

        {/* Messages Area */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 p-4 relative">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.sender === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`max-w-[80%] rounded-xl px-4 py-3 backdrop-blur-sm transition-all duration-300 ${
                    message.sender === "user"
                      ? "bg-gradient-blue text-white shadow-glow-blue"
                      : "card-glass border border-white/10"
                  }`}
                >
                  <p className={message.sender === "user" ? "text-white" : "text-white"}>{message.content}</p>
                  <p className={`text-xs mt-2 ${
                    message.sender === "user" ? "text-white/80" : "text-white/60"
                  }`}>
                    {new Date(message.timestamp).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="p-4 card-glass border-t border-white/5 relative">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSendMessage();
            }}
            className="flex items-center gap-3"
          >
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask something..."
              className="bg-glass/50 backdrop-blur-sm border border-neon-purple/20 text-white placeholder:text-white/60 focus:border-neon-purple/40 focus:shadow-glow-purple outline-none transition-all duration-300"
            />
            <Button
              type="submit"
              size="icon"
              disabled={!inputValue.trim()}
              className="btn-glow-purple text-white h-10 w-10"
            >
              <ArrowUp className="h-5 w-5" />
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
