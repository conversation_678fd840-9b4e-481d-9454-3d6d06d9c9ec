
import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowUp, Loader2 } from "lucide-react";
import { aiService, type UserData } from "@/services/aiService";

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: string;
  isLoading?: boolean;
}

interface ChatBotProps {
  userData?: UserData;
}

const initMessages: Message[] = [
  {
    id: "1",
    content: "Hi there! I'm your VitalEdge assistant. How can I help with your health and fitness goals today?",
    sender: "bot",
    timestamp: new Date().toISOString(),
  },
];

const ChatBot = ({ userData }: ChatBotProps) => {
  const [messages, setMessages] = useState<Message[]>(() => {
    const savedMessages = localStorage.getItem("vitalEdgeChatMessages");
    return savedMessages ? JSON.parse(savedMessages) : initMessages;
  });
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    localStorage.setItem("vitalEdgeChatMessages", JSON.stringify(messages));
    // Scroll to bottom when messages change
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userInput = inputValue.trim();
    setInputValue("");
    setIsLoading(true);

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: userInput,
      sender: "user",
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: "Thinking...",
      sender: "bot",
      timestamp: new Date().toISOString(),
      isLoading: true,
    };

    setMessages((prev) => [...prev, loadingMessage]);

    try {
      // Generate AI response
      const responseContent = await aiService.generateResponse(userInput, userData);

      // Replace loading message with actual response
      setMessages((prev) =>
        prev.map((msg) =>
          msg.isLoading
            ? { ...msg, content: responseContent, isLoading: false }
            : msg
        )
      );
    } catch (error) {
      console.error('Error generating response:', error);

      // Replace loading message with error message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.isLoading
            ? {
                ...msg,
                content: "I'm having trouble connecting right now. Please try again in a moment.",
                isLoading: false
              }
            : msg
        )
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-vital-gray-dark rounded-lg overflow-hidden flex flex-col h-full">
      <div className="px-4 py-3 bg-vital-gray-medium">
        <h2 className="text-xl font-bold text-white">VitalEdge Assistant</h2>
        <p className="text-gray-400 text-sm">Ask me anything about fitness and health</p>
      </div>

      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4 relative">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${
                message.sender === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`max-w-[80%] rounded-xl px-4 py-3 backdrop-blur-sm transition-all duration-300 ${
                  message.sender === "user"
                    ? "bg-gradient-blue text-white shadow-glow-blue"
                    : "card-glass border border-white/10"
                }`}
              >
                <div className="flex items-center gap-2">
                  {message.isLoading && (
                    <Loader2 className="h-4 w-4 animate-spin text-neon-blue" />
                  )}
                  <p className={message.sender === "user" ? "text-white" : "text-white"}>
                    {message.content}
                  </p>
                </div>
                <p className={`text-xs mt-2 ${
                  message.sender === "user" ? "text-white/80" : "text-white/60"
                }`}>
                  {new Date(message.timestamp).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      <div className="p-4 card-glass border-t border-white/5 relative">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSendMessage();
          }}
          className="flex items-center gap-3"
        >
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask something..."
            disabled={isLoading}
            className="bg-glass/50 backdrop-blur-sm border border-neon-purple/20 text-white placeholder:text-white/60 focus:border-neon-purple/40 focus:shadow-glow-purple outline-none transition-all duration-300"
          />
          <Button
            type="submit"
            size="icon"
            disabled={!inputValue.trim() || isLoading}
            className="btn-glow-purple text-white h-10 w-10"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <ArrowUp className="h-5 w-5" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ChatBot;
