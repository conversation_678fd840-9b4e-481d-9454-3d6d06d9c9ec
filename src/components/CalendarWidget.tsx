import { useState } from 'react';

interface CalendarEvent {
  id: number;
  title: string;
  date: string;
  type: 'workout' | 'nutrition' | 'hydration' | 'rest';
}

const CalendarWidget = () => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [events] = useState<CalendarEvent[]>([
    { id: 1, title: 'Upper Body Workout', date: '2023-06-15', type: 'workout' },
    { id: 2, title: 'Nutrition Planning', date: '2023-06-17', type: 'nutrition' },
    { id: 3, title: 'Rest Day', date: '2023-06-18', type: 'rest' },
    { id: 4, title: 'Cardio Session', date: '2023-06-20', type: 'workout' },
    { id: 5, title: 'Hydration Focus', date: '2023-06-22', type: 'hydration' },
  ]);

  const daysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const firstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const formatDate = (year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };

  const getEventsForDate = (year: number, month: number, day: number) => {
    const dateString = formatDate(year, month, day);
    return events.filter(event => event.date === dateString);
  };

  const renderCalendar = () => {
    const days = [];
    const totalDays = daysInMonth(currentMonth);
    const firstDay = firstDayOfMonth(currentMonth);
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-10 bg-glass/30 rounded-lg opacity-30"></div>
      );
    }

    // Add cells for each day of the month
    for (let day = 1; day <= totalDays; day++) {
      const dateEvents = getEventsForDate(year, month, day);
      const hasEvents = dateEvents.length > 0;
      const isToday = new Date().getDate() === day &&
                      new Date().getMonth() === month &&
                      new Date().getFullYear() === year;

      days.push(
        <div
          key={day}
          className={`h-10 flex flex-col justify-center items-center rounded-lg transition-all duration-300 ${
            isToday
              ? 'bg-glass border border-neon-blue/30'
              : hasEvents
                ? 'bg-glass/50 hover:border hover:border-neon-purple/30 cursor-pointer'
                : 'bg-glass/30 hover:bg-glass/50'
          }`}
        >
          <div className="text-sm font-medium text-white">{day}</div>
          {hasEvents && (
            <div className="flex mt-1 space-x-1">
              {dateEvents.map(event => (
                <div
                  key={event.id}
                  className={`w-1.5 h-1.5 rounded-full ${
                    event.type === 'workout' ? 'bg-neon-blue' :
                    event.type === 'nutrition' ? 'bg-neon-purple' :
                    event.type === 'hydration' ? 'bg-blue-500' :
                    'bg-gray-400'
                  }`}
                  title={event.title}
                ></div>
              ))}
            </div>
          )}
        </div>
      );
    }

    return days;
  };

  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (
    <div className="card-neumorphic p-5">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-neon-purple">Calendar</h3>
        <div className="flex items-center space-x-4">
          <button
            onClick={prevMonth}
            className="text-white hover:text-neon-purple transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span className="text-white font-medium">
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </span>
          <button
            onClick={nextMonth}
            className="text-white hover:text-neon-purple transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-2 mb-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-white/70 text-xs font-medium">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-2">
        {renderCalendar()}
      </div>

      <div className="mt-4 flex justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-neon-blue mr-1"></div>
            <span className="text-xs text-white/70">Workout</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 rounded-full bg-neon-purple mr-1"></div>
            <span className="text-xs text-white/70">Nutrition</span>
          </div>
        </div>
        <button className="text-xs text-neon-blue hover:underline">View All</button>
      </div>
    </div>
  );
};

export default CalendarWidget;
