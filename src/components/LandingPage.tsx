
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowDown, Menu } from "lucide-react";
import { useState, useEffect } from "react";

interface LandingPageProps {
  onNavigate: (page: string) => void;
}

const LandingPage = ({ onNavigate }: LandingPageProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const motivationalQuotes = [
    "Transform your body, elevate your mind",
    "Every rep counts, every meal matters",
    "Your only limit is you"
  ];

  const [currentQuote, setCurrentQuote] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % motivationalQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-black/80 backdrop-blur-md border-b border-vital-lime/20">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <div className="text-2xl font-black text-vital-lime">VitalEdge</div>
          <Button variant="ghost" size="icon" className="text-vital-lime hover:bg-vital-lime/10">
            <Menu className="h-6 w-6" />
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center bg-gradient-to-br from-black via-vital-gray-dark to-black">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(50,205,50,0.1)_0%,transparent_70%)]" />
        
        <div className={`text-center z-10 px-6 ${isVisible ? 'animate-slide-up' : 'opacity-0'}`}>
          <div className="mb-8">
            <h1 className="text-6xl md:text-8xl font-black mb-4 bg-gradient-to-r from-white to-vital-lime bg-clip-text text-transparent">
              VitalEdge
            </h1>
            <div className="h-16 flex items-center justify-center">
              <p className="text-xl md:text-2xl text-gray-300 transition-all duration-500">
                {motivationalQuotes[currentQuote]}
              </p>
            </div>
          </div>
          
          <Button 
            onClick={() => onNavigate('generate')}
            className="glow-button bg-vital-lime text-black hover:bg-vital-lime-bright font-bold text-lg px-12 py-6 rounded-full animate-glow-pulse transition-all duration-300 hover:scale-105"
          >
            Generate My Plan
          </Button>
        </div>

        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ArrowDown className="h-8 w-8 text-vital-lime" />
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vital-lime">
            About VitalEdge
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                title: "AI-Powered Plans",
                description: "Personalized workout and nutrition plans tailored to your unique goals and lifestyle",
                icon: "🧠"
              },
              {
                title: "Progress Tracking",
                description: "Monitor your journey with detailed analytics and motivational insights",
                icon: "📊"
              },
              {
                title: "Holistic Wellness",
                description: "Comprehensive approach including fitness, nutrition, hydration, and sleep optimization",
                icon: "🌟"
              }
            ].map((feature, index) => (
              <Card key={index} className="bg-vital-gray-dark border-vital-lime/20 hover:border-vital-lime/50 transition-all duration-300 hover:scale-105 animate-scale-in" style={{ animationDelay: `${index * 0.2}s` }}>
                <CardContent className="p-8 text-center">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-vital-lime mb-4">{feature.title}</h3>
                  <p className="text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-6 bg-vital-gray-dark/50">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vital-lime">
            How It Works
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Enter Your Goals",
                description: "Tell us about your fitness objectives, schedule, and preferences"
              },
              {
                step: "02", 
                title: "AI Generates Plan",
                description: "Our AI creates a personalized workout and nutrition plan just for you"
              },
              {
                step: "03",
                title: "Achieve Success",
                description: "Follow your plan, track progress, and celebrate your victories"
              }
            ].map((step, index) => (
              <div key={index} className="text-center animate-fade-in" style={{ animationDelay: `${index * 0.3}s` }}>
                <div className="text-6xl font-black text-vital-lime/30 mb-4">{step.step}</div>
                <h3 className="text-2xl font-bold text-vital-lime mb-4">{step.title}</h3>
                <p className="text-gray-300">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-6">
        <div className="container mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold text-center mb-16 text-vital-lime">
            Success Stories
          </h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            {[
              {
                name: "Sarah Chen",
                achievement: "Lost 30 lbs in 4 months",
                quote: "VitalEdge transformed my relationship with fitness. The personalized plans made all the difference!"
              },
              {
                name: "Marcus Johnson", 
                achievement: "Gained 15 lbs muscle",
                quote: "The AI really understands my schedule. I've never been more consistent with my workouts."
              }
            ].map((testimonial, index) => (
              <Card key={index} className="bg-vital-gray-dark border-vital-lime/20 animate-scale-in" style={{ animationDelay: `${index * 0.2}s` }}>
                <CardContent className="p-8">
                  <p className="text-gray-300 mb-6 italic">"{testimonial.quote}"</p>
                  <div>
                    <p className="font-bold text-vital-lime">{testimonial.name}</p>
                    <p className="text-sm text-gray-400">{testimonial.achievement}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 bg-black border-t border-vital-lime/20">
        <div className="container mx-auto text-center">
          <div className="text-2xl font-black text-vital-lime mb-4">VitalEdge</div>
          <p className="text-gray-400 mb-8">Empowering your fitness journey with AI-driven insights</p>
          
          <div className="flex justify-center space-x-8 mb-8">
            {['Privacy', 'Terms', 'Contact', 'Support'].map((link) => (
              <a key={link} href="#" className="text-gray-400 hover:text-vital-lime transition-colors">
                {link}
              </a>
            ))}
          </div>
          
          <p className="text-gray-600">© 2024 VitalEdge. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
