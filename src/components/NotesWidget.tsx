import { useState, useEffect } from 'react';

interface Note {
  id: string;
  content: string;
  createdAt: string;
}

const NotesWidget = () => {
  const [notes, setNotes] = useState<Note[]>(() => {
    const savedNotes = localStorage.getItem("vitalEdgeNotes");
    return savedNotes ? JSON.parse(savedNotes) : [
      {
        id: "1",
        content: "Remember to increase protein intake on workout days",
        createdAt: new Date().toISOString()
      },
      {
        id: "2",
        content: "Try the new stretching routine before bed",
        createdAt: new Date().toISOString()
      }
    ];
  });
  
  const [newNote, setNewNote] = useState("");
  
  // Save notes to localStorage when they change
  useEffect(() => {
    localStorage.setItem("vitalEdgeNotes", JSON.stringify(notes));
  }, [notes]);
  
  const addNote = () => {
    if (!newNote.trim()) return;
    
    const note: Note = {
      id: Date.now().toString(),
      content: newNote,
      createdAt: new Date().toISOString()
    };
    
    setNotes([note, ...notes]);
    setNewNote("");
  };
  
  const deleteNote = (id: string) => {
    setNotes(notes.filter(note => note.id !== id));
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  return (
    <div className="card-neumorphic p-5">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold text-neon-blue">Quick Notes</h3>
      </div>
      
      <div className="mb-4">
        <div className="flex">
          <input
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Add a new note..."
            className="flex-1 p-3 rounded-l-lg bg-glass/50 border border-neon-blue/20 text-white focus-glow outline-none"
            onKeyDown={(e) => e.key === "Enter" && addNote()}
          />
          <button
            onClick={addNote}
            disabled={!newNote.trim()}
            className="py-3 px-4 rounded-r-lg btn-glow-blue text-white font-medium disabled:opacity-50"
          >
            Add
          </button>
        </div>
      </div>
      
      <div className="space-y-3 max-h-[250px] overflow-y-auto pr-2 custom-scrollbar">
        {notes.length === 0 ? (
          <p className="text-white/70 text-center py-4">No notes yet. Add some!</p>
        ) : (
          notes.map((note) => (
            <div
              key={note.id}
              className="p-3 rounded-lg bg-glass/50 group hover:shadow-glow-blue transition-all duration-300"
            >
              <div className="flex justify-between items-start">
                <p className="text-white text-sm">{note.content}</p>
                <button
                  onClick={() => deleteNote(note.id)}
                  className="text-white/40 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <p className="text-white/50 text-xs mt-2">{formatDate(note.createdAt)}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default NotesWidget;
