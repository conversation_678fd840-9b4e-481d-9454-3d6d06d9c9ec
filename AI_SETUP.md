# AI Assistant Setup

## Overview
The VitalEdge AI Assistant is powered by Google's Gemini 1.5 Flash model (FREE) and provides personalized health and fitness advice based on your current progress and goals.

## Features
- **Personalized Responses**: Uses your current stats (calories, hydration, streak, XP, level) to provide tailored advice
- **Expert Knowledge**: Covers fitness, nutrition, hydration, recovery, and goal setting
- **Fallback System**: Works even without an API key using intelligent fallback responses
- **Real-time Context**: Understands your current progress and provides relevant suggestions
- **FREE to Use**: Google Gemini offers generous free tier limits

## Setup Instructions

### 1. Get a FREE Gemini API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key (it starts with `AIza`)

### 2. Configure the Environment
1. Open the `.env` file in the project root
2. Replace `your_gemini_api_key_here` with your actual API key:
   ```
   VITE_GEMINI_API_KEY=AIzaSy...your-actual-api-key-here
   ```
3. Save the file

### 3. Restart the Development Server
```bash
npm run dev
```

## System Prompt
The AI assistant uses a comprehensive system prompt that includes:

- **Your Current Stats**: Real-time data about calories, hydration, streak, XP, and level
- **Personality**: Encouraging, motivational, evidence-based, and realistic
- **Expertise Areas**: Fitness, nutrition, hydration, recovery, and goal setting
- **Response Guidelines**: Concise but comprehensive, actionable advice, progress acknowledgment

## Fallback System
If no API key is provided, the assistant will:
- Use intelligent pattern matching for common questions
- Provide contextual responses based on your current stats
- Offer helpful fitness and nutrition advice
- Maintain the same encouraging personality

## Usage Tips
1. **Be Specific**: Ask detailed questions for better responses
2. **Mention Goals**: Tell the assistant about your specific fitness goals
3. **Ask for Plans**: Request workout routines, meal plans, or hydration strategies
4. **Progress Updates**: The assistant automatically sees your current progress

## Example Questions
- "What should I eat to reach my calorie goal?"
- "How can I improve my workout routine?"
- "I'm struggling with hydration, any tips?"
- "What's the best recovery strategy for my current streak?"
- "How can I optimize my nutrition for muscle gain?"

## Privacy & Security
- Your API key is stored locally in the `.env` file
- Conversations are saved locally in your browser
- No personal data is sent to external servers except Google Gemini (when using the API)
- You can clear conversation history anytime

## Troubleshooting
- **No responses**: Check if your API key is correctly set in `.env`
- **Error messages**: Verify your API key is valid and active
- **Slow responses**: Gemini API may be experiencing high traffic
- **Fallback responses**: The system is working without an API key (this is normal)
- **Rate limits**: Free tier has generous limits, but you may hit them with heavy usage

## Cost Considerations
- **Gemini 1.5 Flash is FREE** with generous usage limits
- Free tier includes:
  - 15 requests per minute
  - 1 million tokens per minute
  - 1,500 requests per day
- No credit card required for the free tier
- Monitor usage in [Google AI Studio](https://aistudio.google.com/)
