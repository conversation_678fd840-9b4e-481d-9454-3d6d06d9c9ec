# AI Assistant Setup

## Overview
The VitalEdge AI Assistant is powered by OpenAI's GPT-3.5-turbo model and provides personalized health and fitness advice based on your current progress and goals.

## Features
- **Personalized Responses**: Uses your current stats (calories, hydration, streak, XP, level) to provide tailored advice
- **Expert Knowledge**: Covers fitness, nutrition, hydration, recovery, and goal setting
- **Fallback System**: Works even without an API key using intelligent fallback responses
- **Real-time Context**: Understands your current progress and provides relevant suggestions

## Setup Instructions

### 1. Get an OpenAI API Key
1. Visit [OpenAI's website](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to the API section
4. Create a new API key
5. Copy the key (it starts with `sk-`)

### 2. Configure the Environment
1. Open the `.env` file in the project root
2. Uncomment the line with `VITE_OPENAI_API_KEY`
3. Replace `your_openai_api_key_here` with your actual API key:
   ```
   VITE_OPENAI_API_KEY=sk-your-actual-api-key-here
   ```
4. Save the file

### 3. Restart the Development Server
```bash
npm run dev
```

## System Prompt
The AI assistant uses a comprehensive system prompt that includes:

- **Your Current Stats**: Real-time data about calories, hydration, streak, XP, and level
- **Personality**: Encouraging, motivational, evidence-based, and realistic
- **Expertise Areas**: Fitness, nutrition, hydration, recovery, and goal setting
- **Response Guidelines**: Concise but comprehensive, actionable advice, progress acknowledgment

## Fallback System
If no API key is provided, the assistant will:
- Use intelligent pattern matching for common questions
- Provide contextual responses based on your current stats
- Offer helpful fitness and nutrition advice
- Maintain the same encouraging personality

## Usage Tips
1. **Be Specific**: Ask detailed questions for better responses
2. **Mention Goals**: Tell the assistant about your specific fitness goals
3. **Ask for Plans**: Request workout routines, meal plans, or hydration strategies
4. **Progress Updates**: The assistant automatically sees your current progress

## Example Questions
- "What should I eat to reach my calorie goal?"
- "How can I improve my workout routine?"
- "I'm struggling with hydration, any tips?"
- "What's the best recovery strategy for my current streak?"
- "How can I optimize my nutrition for muscle gain?"

## Privacy & Security
- Your API key is stored locally in the `.env` file
- Conversations are saved locally in your browser
- No personal data is sent to external servers except OpenAI (when using the API)
- You can clear conversation history anytime

## Troubleshooting
- **No responses**: Check if your API key is correctly set in `.env`
- **Error messages**: Verify your API key is valid and has credits
- **Slow responses**: OpenAI API may be experiencing high traffic
- **Fallback responses**: The system is working without an API key (this is normal)

## Cost Considerations
- GPT-3.5-turbo is very cost-effective (typically $0.002 per 1K tokens)
- Average conversation costs less than $0.01
- Monitor usage in your OpenAI dashboard
- Set usage limits in your OpenAI account if needed
